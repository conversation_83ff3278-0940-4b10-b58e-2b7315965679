{"errorMessage": "Authorization failed - please check your credentials", "errorDescription": "<!DOCTYPE html><style nonce=\"Yq2aPITfzy_qYIs-PtVuDg\">body{height:100%;margin:0;width:100%}@media (max-height:350px){.button{font-size:10px}.button-container{margin-top:16px}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{padding:4px 12px}.title-text{font-size:22px;line-height:24px}.subtitle-text{font-size:12px;line-height:18px}}@media (min-height:350px){.button{font-size:14px}.button-container{margin-top:16px}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{padding:12px 24px}.title-text{font-size:28px;line-height:36px}.subtitle-text{font-size:16px;line-height:24px}}.document-root{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;inset:0;position:absolute}.error,.login,.request-storage-access{display:none}.error,.login,.request-storage-access,.too-many-login-redirects{margin:auto;padding:36px}.document-root.show-error .error,.document-root.show-login-page .login,.document-root.show-storage-access .request-storage-access,.too-many-login-redirects{-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.button-container{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.button{border:none;cursor:pointer;color:#0b57d0;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;font-family:Google Sans Text,Roboto,sans-serif;border-radius:100px;padding:12px;margin:0 8px;text-decoration:none}.button:hover{background-color:rgba(11,87,208,.078)}.button:active,.button:focus{background-color:rgba(11,87,208,.122)}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{background-color:#0b57d0;color:#fff}.button.primary-button:hover{box-shadow:0 1px 3px 1px rgba(0,0,0,.149),0 1px 2px 0 rgba(0,0,0,.302)}.icon{height:48px;margin-bottom:16px}.title-text{font-family:Google Sans,Roboto,sans-serif;text-align:center}.subtitle-text{font-family:Google Sans Text,Roboto,sans-serif;margin-top:16px;text-align:center}\n/*# sourceMappingURL=style.css.map */</style><script nonce=\"7UlVWu68NHUr1t734q-ehA\">'use strict';function h(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function k(a){var b=typeof Symbol!=\"undefined\"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length==\"number\")return{next:h(a)};throw Error(String(a)+\" is not an iterable or ArrayLike\");};var l=[\"storage_access_granted\",\"not_in_iframe\",\"login_counter\"];function m(a,b,c){c=c===void 0?\"true\":c;a=new URL(a);for(var d=0;d<l.length;d++)a.searchParams.delete(l[d]);a.searchParams.set(b,c);return a.toString()};/*\n\n Copyright The Closure Library Authors.\n SPDX-License-Identifier: Apache-2.0\n*/\n/*\n\n Copyright Google LLC\n SPDX-License-Identifier: Apache-2.0\n*/\nfunction n(){var a=new p,b=new q,c=document.getElementsByClassName(\"document-root\")[0],d=this;this.g=new r;this.h=a;this.l=b;this.i=c;c.getElementsByClassName(\"accept-button\")[0].addEventListener(\"click\",function(){return void t(d)});c.getElementsByClassName(\"sign-in-button\")[0].addEventListener(\"click\",function(e){return void u(d,e)})}\nfunction v(){var a=new n;w()?x()||typeof document.hasStorageAccess!==\"function\"||typeof document.requestStorageAccess!==\"function\"?y(a,\"show-login-page\"):a.h.hasStorageAccess().then(function(b){b?y(a,\"show-login-page\"):z().then(function(c){c===\"prompt\"?y(a,\"show-storage-access\"):c===\"granted\"?t(a):y(a,\"show-error\")})},function(){y(a,\"show-error\")}):A(a,window.location.href,\"not_in_iframe\")}\nfunction A(a,b,c){c=c?m(b,c):b;if(a.g.get()){if(b=a.g.get())c=B(c),c=C(c),c!==void 0&&(b.action=c);a.g.submit()}else window.location.href===c?window.location.reload():(a=window.location,b=B(c)||D,b=C(b),b!==void 0&&(a.href=b))}function y(a,b){a.i.className=\"document-root \"+b}function t(a){a.h.requestStorageAccess().then(function(){A(a,window.location.href,\"storage_access_granted\")},function(){y(a,\"show-error\")})}\nfunction u(a,b){var c;if(b=(c=b.currentTarget)==null?void 0:c.getAttribute(\"data-popup-url\")){var d=E(window,B(b)||D);F(a.l,function(){d&&d.close();var e=window.location.href;var f=(new URL(e)).searchParams,g=1;f.has(\"login_counter\")&&(f=Number(f.get(\"login_counter\")),isFinite(f)&&(g=f+1));e=m(e,\"login_counter\",String(g));A(a,e)})}};function G(a){this.g=a}G.prototype.toString=function(){return this.g};var D=new G(\"about:invalid#zClosurez\");function H(a){this.j=a}function I(a){return new H(function(b){return b.substr(0,a.length+1).toLowerCase()===a+\":\"})}var J=[I(\"data\"),I(\"http\"),I(\"https\"),I(\"mailto\"),I(\"ftp\"),new H(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function B(a){var b=b===void 0?J:b;if(a instanceof G)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof H&&d.j(a))return new G(a)}}var K=/^\\s*(?!javascript:)(?:[\\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;\nfunction C(a){if(a instanceof G)if(a instanceof G)a=a.g;else throw Error(\"\");else a=K.test(a)?a:void 0;return a};function E(a,b){b=C(b);return b!==void 0?a.open(b,\"popupWindow\",\"popup=yes,height=500,width=690\"):null};function r(){}r.prototype.get=function(){return document.querySelector(\"form\")};r.prototype.submit=function(){var a;(a=this.get())==null||a.submit()};function L(a){for(var b=k(document.cookie.split(\";\")),c=b.next();!c.done;c=b.next())if(c=c.value.split(\"=\"),c[0].trim()===a)return c[1]};function q(){this.h=[\"SAPISID\",\"__Secure-1PAPISID\",\"__Secure-3PAPISID\"];this.g=void 0}function F(a,b){a.g&&clearInterval(a.g);for(var c={},d=k(a.h),e=d.next();!e.done;e=d.next())e=e.value,c[e]=L(e);a.g=setInterval(function(){a:{var f=k(a.h);for(var g=f.next();!g.done;g=f.next())if(g=g.value,L(g)!==void 0&&c[g]!==L(g)){f=!0;break a}f=!1}f&&(clearInterval(a.g),a.g=void 0,b())},1E3)};function w(){var a=!0;try{a=window.self!==window.top}catch(b){}return a};function p(){}p.prototype.hasStorageAccess=function(){return document.hasStorageAccess()};function z(){return navigator.permissions.query({name:\"storage-access\"}).then(function(a){return a.state}).catch(function(){return\"prompt\"})}p.prototype.requestStorageAccess=function(){return document.requestStorageAccess()};\nfunction x(){if(window.navigator.userAgentData&&window.navigator.userAgentData.brands)for(var a=window.navigator.userAgentData.brands,b=0;b<a.length;b++){var c=a[b];if(c.brand===\"Google Chrome\")return c.version===\"115\"||c.version===\"116\"}return!1};document.readyState===\"complete\"?v():document.addEventListener(\"DOMContentLoaded\",M);function M(){v()};\n</script><div class=\"document-root loading\"><div class=\"request-storage-access\"><div><img src=&#47;&#47;ssl.gstatic.com&#47;docs&#47;common&#47;product&#47;sheets_app_icon1.png alt=Google&#32;Spreadsheets class=\"icon\"></div><div class=\"title-text\">Geef Google Spreadsheets toegang tot benodigde cookies</div><div class=\"subtitle-text\">Je hebt geen toegang tot deze content als de benodigde cookies uitstaan</div><div class=\"button-container\"><a target=\"cookieAccessHelp\" href=\"https://support.google.com/drive?p=enable_storage_access\" class=\" button\">Meer informatie</a><button type=\"button\" class=\"accept-button button primary-button\">Cookies toestaan</button></div></div><div class=\"login\"><div><img src=https:&#47;&#47;www.gstatic.com&#47;images&#47;branding&#47;googleg&#47;1x&#47;googleg_standard_color_48dp.png alt=Google&#45;logo class=\"icon\"></div><div class=\"title-text\">Meld u aan bij je Google-account</div><div class=\"subtitle-text\">Je moet inloggen om toegang te krijgen tot deze content</div><div class=\"button-container\"><button type=\"button\" class=\"sign-in-button button primary-button\" data-popup-url=https:&#47;&#47;accounts.google.com&#47;ServiceLogin?continue&#61;https:&#47;&#47;docs.google.com&#47;spreadsheets&#47;d&#47;1GydiLM0iwiT1JHHdyeM8buB&#45;2jeK9rK3_rDOupiTRes&#47;export?format%3Dcsv%26gid%3D0&amp;btmpl&#61;popup&amp;hl&#61;nl_NL>Inloggen</button></div></div><div class=\"error\"><div><img src=https:&#47;&#47;www.gstatic.com&#47;images&#47;branding&#47;googleg&#47;1x&#47;googleg_standard_color_48dp.png alt=Google&#45;logo class=\"icon\"></div><div class=\"title-text\">Kan geen toegang krijgen tot je Google-account</div><div class=\"subtitle-text\">We hebben op dit moment geen toegang tot deze content. Log in op je Google-account of sta cookietoegang toe om door te gaan.</div><div class=\"button-container\"><a target=\"cookieAccessHelp\" href=\"https://support.google.com/drive?p=enable_storage_access\" class=\"primary-button button\">Meer informatie</a></div></div></div>", "errorDetails": {"rawErrorMessage": ["401 - \"<!DOCTYPE html><style nonce=\\\"Yq2aPITfzy_qYIs-PtVuDg\\\">body{height:100%;margin:0;width:100%}@media (max-height:350px){.button{font-size:10px}.button-container{margin-top:16px}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{padding:4px 12px}.title-text{font-size:22px;line-height:24px}.subtitle-text{font-size:12px;line-height:18px}}@media (min-height:350px){.button{font-size:14px}.button-container{margin-top:16px}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{padding:12px 24px}.title-text{font-size:28px;line-height:36px}.subtitle-text{font-size:16px;line-height:24px}}.document-root{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;inset:0;position:absolute}.error,.login,.request-storage-access{display:none}.error,.login,.request-storage-access,.too-many-login-redirects{margin:auto;padding:36px}.document-root.show-error .error,.document-root.show-login-page .login,.document-root.show-storage-access .request-storage-access,.too-many-login-redirects{-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.button-container{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.button{border:none;cursor:pointer;color:#0b57d0;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;font-family:Google Sans Text,Roboto,sans-serif;border-radius:100px;padding:12px;margin:0 8px;text-decoration:none}.button:hover{background-color:rgba(11,87,208,.078)}.button:active,.button:focus{background-color:rgba(11,87,208,.122)}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{background-color:#0b57d0;color:#fff}.button.primary-button:hover{box-shadow:0 1px 3px 1px rgba(0,0,0,.149),0 1px 2px 0 rgba(0,0,0,.302)}.icon{height:48px;margin-bottom:16px}.title-text{font-family:Google Sans,Roboto,sans-serif;text-align:center}.subtitle-text{font-family:Google Sans Text,Roboto,sans-serif;margin-top:16px;text-align:center}\\n/*# sourceMappingURL=style.css.map */</style><script nonce=\\\"7UlVWu68NHUr1t734q-ehA\\\">'use strict';function h(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function k(a){var b=typeof Symbol!=\\\"undefined\\\"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length==\\\"number\\\")return{next:h(a)};throw Error(String(a)+\\\" is not an iterable or ArrayLike\\\");};var l=[\\\"storage_access_granted\\\",\\\"not_in_iframe\\\",\\\"login_counter\\\"];function m(a,b,c){c=c===void 0?\\\"true\\\":c;a=new URL(a);for(var d=0;d<l.length;d++)a.searchParams.delete(l[d]);a.searchParams.set(b,c);return a.toString()};/*\\n\\n Copyright The Closure Library Authors.\\n SPDX-License-Identifier: Apache-2.0\\n*/\\n/*\\n\\n Copyright Google LLC\\n SPDX-License-Identifier: Apache-2.0\\n*/\\nfunction n(){var a=new p,b=new q,c=document.getElementsByClassName(\\\"document-root\\\")[0],d=this;this.g=new r;this.h=a;this.l=b;this.i=c;c.getElementsByClassName(\\\"accept-button\\\")[0].addEventListener(\\\"click\\\",function(){return void t(d)});c.getElementsByClassName(\\\"sign-in-button\\\")[0].addEventListener(\\\"click\\\",function(e){return void u(d,e)})}\\nfunction v(){var a=new n;w()?x()||typeof document.hasStorageAccess!==\\\"function\\\"||typeof document.requestStorageAccess!==\\\"function\\\"?y(a,\\\"show-login-page\\\"):a.h.hasStorageAccess().then(function(b){b?y(a,\\\"show-login-page\\\"):z().then(function(c){c===\\\"prompt\\\"?y(a,\\\"show-storage-access\\\"):c===\\\"granted\\\"?t(a):y(a,\\\"show-error\\\")})},function(){y(a,\\\"show-error\\\")}):A(a,window.location.href,\\\"not_in_iframe\\\")}\\nfunction A(a,b,c){c=c?m(b,c):b;if(a.g.get()){if(b=a.g.get())c=B(c),c=C(c),c!==void 0&&(b.action=c);a.g.submit()}else window.location.href===c?window.location.reload():(a=window.location,b=B(c)||D,b=C(b),b!==void 0&&(a.href=b))}function y(a,b){a.i.className=\\\"document-root \\\"+b}function t(a){a.h.requestStorageAccess().then(function(){A(a,window.location.href,\\\"storage_access_granted\\\")},function(){y(a,\\\"show-error\\\")})}\\nfunction u(a,b){var c;if(b=(c=b.currentTarget)==null?void 0:c.getAttribute(\\\"data-popup-url\\\")){var d=E(window,B(b)||D);F(a.l,function(){d&&d.close();var e=window.location.href;var f=(new URL(e)).searchParams,g=1;f.has(\\\"login_counter\\\")&&(f=Number(f.get(\\\"login_counter\\\")),isFinite(f)&&(g=f+1));e=m(e,\\\"login_counter\\\",String(g));A(a,e)})}};function G(a){this.g=a}G.prototype.toString=function(){return this.g};var D=new G(\\\"about:invalid#zClosurez\\\");function H(a){this.j=a}function I(a){return new H(function(b){return b.substr(0,a.length+1).toLowerCase()===a+\\\":\\\"})}var J=[I(\\\"data\\\"),I(\\\"http\\\"),I(\\\"https\\\"),I(\\\"mailto\\\"),I(\\\"ftp\\\"),new H(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function B(a){var b=b===void 0?J:b;if(a instanceof G)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof H&&d.j(a))return new G(a)}}var K=/^\\\\s*(?!javascript:)(?:[\\\\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;\\nfunction C(a){if(a instanceof G)if(a instanceof G)a=a.g;else throw Error(\\\"\\\");else a=K.test(a)?a:void 0;return a};function E(a,b){b=C(b);return b!==void 0?a.open(b,\\\"popupWindow\\\",\\\"popup=yes,height=500,width=690\\\"):null};function r(){}r.prototype.get=function(){return document.querySelector(\\\"form\\\")};r.prototype.submit=function(){var a;(a=this.get())==null||a.submit()};function L(a){for(var b=k(document.cookie.split(\\\";\\\")),c=b.next();!c.done;c=b.next())if(c=c.value.split(\\\"=\\\"),c[0].trim()===a)return c[1]};function q(){this.h=[\\\"SAPISID\\\",\\\"__Secure-1PAPISID\\\",\\\"__Secure-3PAPISID\\\"];this.g=void 0}function F(a,b){a.g&&clearInterval(a.g);for(var c={},d=k(a.h),e=d.next();!e.done;e=d.next())e=e.value,c[e]=L(e);a.g=setInterval(function(){a:{var f=k(a.h);for(var g=f.next();!g.done;g=f.next())if(g=g.value,L(g)!==void 0&&c[g]!==L(g)){f=!0;break a}f=!1}f&&(clearInterval(a.g),a.g=void 0,b())},1E3)};function w(){var a=!0;try{a=window.self!==window.top}catch(b){}return a};function p(){}p.prototype.hasStorageAccess=function(){return document.hasStorageAccess()};function z(){return navigator.permissions.query({name:\\\"storage-access\\\"}).then(function(a){return a.state}).catch(function(){return\\\"prompt\\\"})}p.prototype.requestStorageAccess=function(){return document.requestStorageAccess()};\\nfunction x(){if(window.navigator.userAgentData&&window.navigator.userAgentData.brands)for(var a=window.navigator.userAgentData.brands,b=0;b<a.length;b++){var c=a[b];if(c.brand===\\\"Google Chrome\\\")return c.version===\\\"115\\\"||c.version===\\\"116\\\"}return!1};document.readyState===\\\"complete\\\"?v():document.addEventListener(\\\"DOMContentLoaded\\\",M);function M(){v()};\\n</script><div class=\\\"document-root loading\\\"><div class=\\\"request-storage-access\\\"><div><img src=&#47;&#47;ssl.gstatic.com&#47;docs&#47;common&#47;product&#47;sheets_app_icon1.png alt=Google&#32;Spreadsheets class=\\\"icon\\\"></div><div class=\\\"title-text\\\">Geef Google Spreadsheets toegang tot benodigde cookies</div><div class=\\\"subtitle-text\\\">Je hebt geen toegang tot deze content als de benodigde cookies uitstaan</div><div class=\\\"button-container\\\"><a target=\\\"cookieAccessHelp\\\" href=\\\"https://support.google.com/drive?p=enable_storage_access\\\" class=\\\" button\\\">Meer informatie</a><button type=\\\"button\\\" class=\\\"accept-button button primary-button\\\">Cookies toestaan</button></div></div><div class=\\\"login\\\"><div><img src=https:&#47;&#47;www.gstatic.com&#47;images&#47;branding&#47;googleg&#47;1x&#47;googleg_standard_color_48dp.png alt=Google&#45;logo class=\\\"icon\\\"></div><div class=\\\"title-text\\\">Meld u aan bij je Google-account</div><div class=\\\"subtitle-text\\\">Je moet inloggen om toegang te krijgen tot deze content</div><div class=\\\"button-container\\\"><button type=\\\"button\\\" class=\\\"sign-in-button button primary-button\\\" data-popup-url=https:&#47;&#47;accounts.google.com&#47;ServiceLogin?continue&#61;https:&#47;&#47;docs.google.com&#47;spreadsheets&#47;d&#47;1GydiLM0iwiT1JHHdyeM8buB&#45;2jeK9rK3_rDOupiTRes&#47;export?format%3Dcsv%26gid%3D0&amp;btmpl&#61;popup&amp;hl&#61;nl_NL>Inloggen</button></div></div><div class=\\\"error\\\"><div><img src=https:&#47;&#47;www.gstatic.com&#47;images&#47;branding&#47;googleg&#47;1x&#47;googleg_standard_color_48dp.png alt=Google&#45;logo class=\\\"icon\\\"></div><div class=\\\"title-text\\\">Kan geen toegang krijgen tot je Google-account</div><div class=\\\"subtitle-text\\\">We hebben op dit moment geen toegang tot deze content. Log in op je Google-account of sta cookietoegang toe om door te gaan.</div><div class=\\\"button-container\\\"><a target=\\\"cookieAccessHelp\\\" href=\\\"https://support.google.com/drive?p=enable_storage_access\\\" class=\\\"primary-button button\\\">Meer informatie</a></div></div></div>\""], "httpCode": "401"}, "n8nDetails": {"nodeName": "Get Exam Numbers Sheet", "nodeType": "n8n-nodes-base.httpRequest", "nodeVersion": 4.2, "itemIndex": 0, "time": "8/1/2025, 8:23:32 PM", "n8nVersion": "1.101.2 (Self Hosted)", "binaryDataMode": "default", "stackTrace": ["NodeApiError: Authorization failed - please check your credentials", "    at ExecuteContext.execute (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-nodes-base@file+packages+nodes-base_@aws-sdk+credential-providers@3.808.0_asn1.js@5_1af219c3f47f2a1223ec4ccec249a974/node_modules/n8n-nodes-base/nodes/HttpRequest/V3/HttpRequestV3.node.ts:780:15)", "    at processTicksAndRejections (node:internal/process/task_queues:105:5)", "    at WorkflowExecute.runNode (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.103.0_encoding@0.1.13_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/src/execution-engine/workflow-execute.ts:1194:9)", "    at /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.103.0_encoding@0.1.13_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/src/execution-engine/workflow-execute.ts:1564:27", "    at /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.103.0_encoding@0.1.13_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/src/execution-engine/workflow-execute.ts:2135:11"]}}