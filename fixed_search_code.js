// البحث عن جميع الأرقام الامتحانية للطالب - إصدار محسن
const userId = $('Telegram Trigger').first().json.message.from.id.toString();
const chatId = $('Telegram Trigger').first().json.message.chat.id;
const examData = $input.all();

console.log('🔍 User ID المطلوب:', userId);
console.log('📊 عدد الصفوف:', examData.length);

let studentsFound = []; // مصفوفة لحفظ جميع التسجيلات

// البحث في البيانات
for (let i = 0; i < examData.length; i++) {
  const item = examData[i];
  
  if (item && item.json) {
    const data = item.json;
    
    // جرب الطرق المختلفة للوصول للبيانات
    let rowUserId, studentName, examNumber;
    
    // الطريقة 1: استخدام A, B, C
    rowUserId = data.A || data['A'];
    studentName = data.B || data['B'];
    examNumber = data.C || data['C'];
    
    // الطريقة 2: استخدام أول ثلاث قيم
    if (!rowUserId) {
      const values = Object.values(data);
      if (values.length >= 3) {
        rowUserId = values[0];
        studentName = values[1];
        examNumber = values[2];
      }
    }
    
    console.log(`🔍 الصف ${i}:`, { rowUserId, studentName, examNumber });
    
    // التحقق من تطابق User ID
    if (rowUserId && rowUserId.toString() === userId) {
      const student = {
        name: studentName || 'غير محدد',
        examNumber: examNumber || 'غير محدد'
      };
      studentsFound.push(student);
      console.log('✅ تم العثور على تسجيل!', student);
      // لا نستخدم break هنا لنستمر في البحث عن المزيد
    }
  }
}

if (studentsFound.length > 0) {
  // إنشاء رسالة مختصرة ومنظمة
  let message = `📋 أرقامك الامتحانية (${studentsFound.length}):\n\n`;
  
  // إضافة الأرقام بشكل مختصر
  studentsFound.forEach((student, index) => {
    message += `${index + 1}. ${student.examNumber} - ${student.name}\n`;
  });
  
  message += `\n🎓 بالتوفيق في الامتحانات!`;
  
  // التحقق من طول الرسالة
  if (message.length > 4000) {
    // إذا كانت الرسالة طويلة، اعرض أول 10 تسجيلات فقط
    message = `📋 أرقامك الامتحانية (${studentsFound.length} - عرض أول 10):\n\n`;
    
    studentsFound.slice(0, 10).forEach((student, index) => {
      message += `${index + 1}. ${student.examNumber} - ${student.name}\n`;
    });
    
    if (studentsFound.length > 10) {
      message += `\n... و ${studentsFound.length - 10} تسجيلات أخرى`;
    }
    
    message += `\n\n🎓 بالتوفيق في الامتحانات!`;
  }
  
  return [{
    json: {
      found: true,
      chatId: chatId,
      count: studentsFound.length,
      students: studentsFound,
      message: message
    }
  }];
} else {
  // جمع User IDs للتشخيص
  const foundIds = [];
  examData.forEach((item, index) => {
    if (item && item.json) {
      const data = item.json;
      const firstValue = data.A || data['A'] || Object.values(data)[0];
      if (firstValue) foundIds.push(firstValue);
    }
  });
  
  return [{
    json: {
      found: false,
      chatId: chatId,
      message: `❌ لم يتم العثور على User ID: ${userId}\n\n🔍 User IDs الموجودة: ${foundIds.slice(0, 5).join(', ')}\n\n📝 تأكد من صحة User ID في الجدول`
    }
  }];
}
