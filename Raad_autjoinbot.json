{"name": "Raad_autjoinbot", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "19b51ae2-3fc3-46b7-acde-8f45f52b0ab8", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [-432, 176], "webhookId": "telegram-webhook", "credentials": {"telegramApi": {"id": "OBuXXAgpVlurgrNb", "name": "Raad_autjoinbot"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.message.text}}", "operation": "startsWith", "value2": "تسجيل"}, {"value1": "={{$json.message.text}}", "value2": "ساعدني"}]}, "combineOperation": "any"}, "id": "d04fc5a0-f383-4aeb-b0b1-36256e123aa8", "name": "Check Register Command", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-208, 176]}, {"parameters": {"jsCode": "// استخراج اسم الطالب من الرسالة\nconst messageText = $input.first().json.message.text;\nconst studentName = messageText.replace('تسجيل', '').trim();\nconst chatId = $input.first().json.message.chat.id;\nconst userId = $input.first().json.message.from.id;\nconst username = $input.first().json.message.from.username || 'غير محدد';\n\n// التحقق من وجود اسم\nif (!studentName) {\n  return [{\n    json: {\n      error: true,\n      message: 'يرجى إدخال اسم الطالب بعد كلمة تسجيل\\nمثال: تسجيل أحمد محمد',\n      chatId: chatId\n    }\n  }];\n}\n\n// إعداد البيانات للإرسال إلى Google Sheets\nreturn [{\n  json: {\n    studentName: studentName,\n    chatId: chatId,\n    userId: userId,\n    username: username,\n    registrationDate: new Date().toISOString(),\n    registrationTime: new Date().toLocaleString('en-US', {\n      timeZone: 'Africa/Cairo'\n    })\n  }\n}];"}, "id": "67ba4d0c-e908-4ad7-bf2f-e3034cda2543", "name": "Extract Student Name", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [16, 576]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.error}}", "value2": true}]}}, "id": "ac5988ca-1e08-4fab-86a6-24cfa94a9355", "name": "Check for Errors", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [256, 544]}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "id": "3743bdd0-ddd4-4585-a7d2-a6da8482aaf0", "name": "Send Error Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [368, 400], "webhookId": "d9dffe5c-d143-4373-b9ef-568d24625e1b", "credentials": {"telegramApi": {"id": "OBuXXAgpVlurgrNb", "name": "Raad_autjoinbot"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1Rt9ZlR0Oq0As3-OTM9jNFifwdnBqmE3YOl4E6vqT5hM", "mode": "id"}, "sheetName": {"__rl": true, "value": "Students", "mode": "name"}, "options": {}}, "id": "6dd5d1dd-4905-4069-8659-680559aa9378", "name": "Check for Duplicate Names", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [480, 560], "credentials": {"googleSheetsOAuth2Api": {"id": "V0t2snsu59aNwKsx", "name": "Google Sheets account 2"}}}, {"parameters": {"jsCode": "// التحقق من وجود اسم مكرر\nconst currentStudentName = $('Extract Student Name').first().json.studentName;\nconst existingStudents = $input.all();\n\n// التحقق من صحة البيانات\nif (!currentStudentName) {\n  return [{\n    json: {\n      isDuplicate: false,\n      error: true,\n      message: 'خطأ: لم يتم العثور على اسم الطالب'\n    }\n  }];\n}\n\n// البحث عن اسم مطابق مع تنظيف البيانات\nconst normalizedCurrentName = currentStudentName.toLowerCase().trim();\nconst isDuplicate = existingStudents.some(student => {\n  if (!student.json || !student.json.studentName) return false;\n  const normalizedExistingName = student.json.studentName.toLowerCase().trim();\n  return normalizedExistingName === normalizedCurrentName;\n});\n\n// إعداد البيانات المشتركة\nconst baseData = {\n  chatId: $('Extract Student Name').first().json.chatId,\n  studentName: currentStudentName\n};\n\nif (isDuplicate) {\n  return [{\n    json: {\n      ...baseData,\n      isDuplicate: true,\n      message: `❌📝 \"${currentStudentName}\" قد تم تسجيلك مسبقاً بالفعل\\n\\n📺 يرجى متابعة قناة الأستاذ رعد @Raad555 بينما تبدأ الدورة 🎓`\n    }\n  }];\n} else {\n  return [{\n    json: {\n      ...baseData,\n      isDuplicate: false\n    }\n  }];\n}"}, "id": "4f763310-d1a0-449c-85fe-06fe5f2e766e", "name": "Process Duplicate Check", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [656, 560]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.isDuplicate}}", "value2": true}]}}, "id": "b5384eac-aa61-4ad5-b372-249c050a177e", "name": "Check If Duplicate", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [816, 560]}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "id": "a2970f33-b6d7-4f3b-abb3-0aad5300b6a6", "name": "Send Duplicate Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [960, 400], "webhookId": "duplicate-error-webhook", "credentials": {"telegramApi": {"id": "OBuXXAgpVlurgrNb", "name": "Raad_autjoinbot"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1Rt9ZlR0Oq0As3-OTM9jNFifwdnBqmE3YOl4E6vqT5hM", "mode": "id"}, "sheetName": {"__rl": true, "value": "Students", "mode": "name"}, "columns": {"mappingMode": "autoMapInputData", "value": {"اسم الطالب": "={{ $json.studentName }}", "هل هو مكرر": "={{ $json.isDuplicate }}", "ايدي الطالب": "={{ $json.chatId }}"}, "matchingColumns": [], "schema": [{"id": "اسم الطالب", "displayName": "اسم الطالب", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ايدي الطالب", "displayName": "ايدي الطالب", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "هل هو مكرر", "displayName": "هل هو مكرر", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "52c2ded1-1cd9-4150-949b-27e47e92c506", "name": "Add to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [1056, 576], "credentials": {"googleSheetsOAuth2Api": {"id": "V0t2snsu59aNwKsx", "name": "Google Sheets account 2"}}}, {"parameters": {"jsCode": "// استرجاع البيانات من العقدة السابقة\nconst extractData = $('Extract Student Name').first().json;\n\n// إرجاع البيانات بشكل واضح\nreturn [{\n  json: {\n    chatId: extractData.chatId,\n    studentName: extractData.studentName,\n    registrationTime: extractData.registrationTime,\n    message: `🎉 تم تسجيل الطالب بنجاح! ✅\n\n👤 اسم الطالب: ${extractData.studentName}\n📅 تاريخ التسجيل: ${extractData.registrationTime}\n\n📢 والان تابع قناة الاستاذ رعد: @Raad555\n⏳ وانتظر في الايام القادمة نشر القائمة التي تحتوي على رقمك الامتحاني لاستخدامه في الامتحانات 📝`\n  }\n}];"}, "id": "73e5fb18-6d01-40f5-b0ec-46d93d278fb2", "name": "Prepare Success Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1168, 576]}, {"parameters": {"chatId": "={{$json.chatId}}", "text": "={{$json.message}}", "additionalFields": {}}, "id": "a11b7926-88bb-41c8-8995-3ded10def825", "name": "Send Success Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1280, 576], "webhookId": "5b048772-c95c-4c35-ac73-f0820198f257", "credentials": {"telegramApi": {"id": "OBuXXAgpVlurgrNb", "name": "Raad_autjoinbot"}}}, {"parameters": {"chatId": "={{$json.message.chat.id}}", "text": "مرحباً بك! 👋✨\n\n📝 الطريقة الصحيحة للتسجيل:\n🔹 تسجيل + اسم الطالب\n\n💡 مثال:\n🔸 تسجيل رعد ابراهيم\n\n🆘 في حال تحتاج أي مساعدة اكتب كلمة: ساعدني", "additionalFields": {"parse_mode": "HTML"}}, "id": "3c4b3a85-641b-49bb-b350-10b81f3d765f", "name": "Send Help Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [0, 192], "webhookId": "4f14410c-c3c7-460c-843d-1d62d7ea5433", "credentials": {"telegramApi": {"id": "OBuXXAgpVlurgrNb", "name": "Raad_autjoinbot"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.message.text}}", "value2": "ساعدني"}]}}, "id": "f2bf5ac0-b5e1-415c-8b9a-10eaaa4dc14b", "name": "Check Help Command", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-192, 464]}, {"parameters": {"chatId": "={{$json.message.chat.id}}", "text": "🆘 تحتاج مساعدة؟\n\n📹 شاهد فيديو شرح طريقة التسجيل:\n<a href=\"https://t.me/saddis_iq/52\">فيديو شرح التسجيل</a>\n\n👨‍💻 تواصل مع مبرمج البوت:\n<a href=\"https://t.me/A_5_6\">@A_5_6</a>\n\n📞 سيقوم بمساعدتك في حل أي مشكلة تواجهها! 🤝", "additionalFields": {"parse_mode": "HTML"}}, "id": "5ac048f5-507b-429c-bd97-e8a6f55abd49", "name": "Send Support Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [16, 416], "webhookId": "support-message-webhook", "credentials": {"telegramApi": {"id": "OBuXXAgpVlurgrNb", "name": "Raad_autjoinbot"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Check Register Command", "type": "main", "index": 0}]]}, "Check Register Command": {"main": [[{"node": "Check Help Command", "type": "main", "index": 0}], [{"node": "Send Help Message", "type": "main", "index": 0}]]}, "Check Help Command": {"main": [[{"node": "Send Support Message", "type": "main", "index": 0}], [{"node": "Extract Student Name", "type": "main", "index": 0}]]}, "Extract Student Name": {"main": [[{"node": "Check for Errors", "type": "main", "index": 0}]]}, "Check for Errors": {"main": [[{"node": "Send Error Message", "type": "main", "index": 0}], [{"node": "Check for Duplicate Names", "type": "main", "index": 0}]]}, "Check for Duplicate Names": {"main": [[{"node": "Process Duplicate Check", "type": "main", "index": 0}]]}, "Process Duplicate Check": {"main": [[{"node": "Check If Duplicate", "type": "main", "index": 0}]]}, "Check If Duplicate": {"main": [[{"node": "Send Duplicate Error", "type": "main", "index": 0}], [{"node": "Add to Google Sheets", "type": "main", "index": 0}]]}, "Add to Google Sheets": {"main": [[{"node": "Prepare Success Data", "type": "main", "index": 0}]]}, "Prepare Success Data": {"main": [[{"node": "Send Success Message", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "3c2695b2-c51c-4c35-b657-93eb51f52981", "meta": {"templateCredsSetupCompleted": true, "instanceId": "96e1128f8135fc72c5e5c12e801df8aad7f24221bb1209de54018f3ce313abf7"}, "id": "AFdn6f1PDtezrouU", "tags": []}