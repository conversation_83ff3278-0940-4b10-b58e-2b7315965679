// البحث عن جميع الأرقام الامتحانية للطالب - إصدار محسن جداً
const userId = $('Telegram Trigger').first().json.message.from.id.toString();
const chatId = $('Telegram Trigger').first().json.message.chat.id;
const examData = $input.all();

console.log('🔍 User ID المطلوب:', userId);
console.log('📊 عدد الصفوف:', examData.length);

let studentsFound = []; // مصفوفة لحفظ جميع التسجيلات

// البحث في البيانات
for (let i = 0; i < examData.length; i++) {
  const item = examData[i];
  
  if (item && item.json) {
    const data = item.json;
    
    // جرب الطرق المختلفة للوصول للبيانات
    let rowUserId, studentName, examNumber;
    
    // الطريقة 1: استخدام A, B, C
    rowUserId = data.A || data['A'];
    studentName = data.B || data['B'];
    examNumber = data.C || data['C'];
    
    // الطريقة 2: استخدام أول ثلاث قيم
    if (!rowUserId) {
      const values = Object.values(data);
      if (values.length >= 3) {
        rowUserId = values[0];
        studentName = values[1];
        examNumber = values[2];
      }
    }
    
    console.log(`🔍 الصف ${i}:`, { rowUserId, studentName, examNumber });
    
    // التحقق من تطابق User ID
    if (rowUserId && rowUserId.toString() === userId) {
      const student = {
        name: studentName || 'غير محدد',
        examNumber: examNumber || 'غير محدد'
      };
      studentsFound.push(student);
      console.log('✅ تم العثور على تسجيل!', student);
      // لا نستخدم break هنا لنستمر في البحث عن المزيد
    }
  }
}

if (studentsFound.length > 0) {
  // إنشاء رسالة مختصرة ومنظمة
  let message = `📋 أرقامك الامتحانية (${studentsFound.length}):\n\n`;
  
  // إضافة الأرقام بشكل مختصر - أقصى 15 تسجيل
  const maxToShow = Math.min(15, studentsFound.length);
  
  for (let i = 0; i < maxToShow; i++) {
    const student = studentsFound[i];
    message += `${i + 1}. ${student.examNumber}\n`;
  }
  
  if (studentsFound.length > 15) {
    message += `\n... و ${studentsFound.length - 15} تسجيلات أخرى`;
  }
  
  message += `\n\n🎓 بالتوفيق في الامتحانات!`;
  
  return [{
    json: {
      found: true,
      chatId: chatId,
      count: studentsFound.length,
      students: studentsFound,
      message: message
    }
  }];
} else {
  // رسالة مختصرة جداً لحالة عدم العثور
  return [{
    json: {
      found: false,
      chatId: chatId,
      message: `❌ لم يتم العثور على User ID: ${userId}\n\n📝 تأكد من تسجيلك في البوت أولاً\n\n💡 أرسل "تسجيل" متبوعاً باسمك للتسجيل`
    }
  }];
}
